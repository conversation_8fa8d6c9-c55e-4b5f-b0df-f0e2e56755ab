<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>开始抽取</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css" media="all">
    <!-- 引入本地Layui JS -->
    <script src="/static/layui/layui.js"></script>
    <style>
        body,
        html {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            touch-action: none;
            -webkit-overflow-scrolling: touch;
        }

        body {
            width: 100vw;
            height: 100vh;
            background-size: cover;
            background-attachment: fixed;
            position: relative;
            transition: background-image 0.8s ease-in-out;
            cursor: pointer;
        }

        /* 全屏状态下的背景修复 */
        body:-webkit-full-screen {
            background-attachment: scroll;
        }

        body:-moz-full-screen {
            background-attachment: scroll;
        }

        body:fullscreen {
            background-attachment: scroll;
        }

        /* 第一页样式 */
        .page1 {
            background-size: cover;
            background-repeat: no-repeat;
            width: 100%;
            height: 100%;
            background-image: url('/static/img/index1/main-img.jpg');
        }

        .title-img {
            position: absolute;
            top: 212px;
            left: 732px;
        }

        .title-img-logo {
            width: 100%;
            height: 100%;
        }

        /* 第2页样式 - 根据页面2.jpg设计 */
        .page2 {
            background-size: cover;
            background-repeat: no-repeat;
            position: relative;
            transition: opacity 0.8s ease-in-out;
            width: 100%;
            height: 100%;
            background-image: url('/static/img/index2/背景图调整.jpg');
        }


        .page2.active {
            display: block;
        }

        /* 第2页主标题 - 按设计图调整 */
        .page2 .logo {
            position: absolute;
            top: 5vh;
            left: 12%;
            transform: translateX(-50%);
            z-index: 10;
        }

        /* 第2页主标题 - 按设计图调整 */
        .page2 .main-title {
            position: absolute;
            top: 5vh;
            left: 52%;
            transform: translateX(-50%);
            /* max-width: 600px;
            width: 55vw; */
            z-index: 10;
        }

        .center-class {
            position: relative;
            padding-top: 15%;
            height: 100%;
            width: 100%;
        }

        .center-base {
            padding-right: 1.5%;
            padding-left: 1.5%;
            position: absolute;
            top: 37%;
            height: 34%;
            display: flex;
            width: 100%;
        }

        .start-button-container {
            position: absolute;
            top: 89%;
            left: 31%;
        }

        .start-button-container .my-btn {
            width: 447px;
            height: 90px;
            font-size: 53px;
            background-image: linear-gradient(to bottom, #ff4500, #e91f15, #bc1127);
            border-radius: 64px;
            color: #ffead4;
            font-weight: bold;
            border: 3px solid #ffb669;
            box-shadow: 0px 15px #b65c55;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .my-btn:hover {
            transform: scale(1.1);
        }

        .page2 .timer-item .button-items {
            display: flex;
            padding-top: 6%;
            justify-content: center;
            height: 21%;
        }

        .start-button-container1 {
            padding-left: 20px;
            padding-right: 20px;
        }

        .page2 .timer-item .button-items .start-button-container1 .my-btn1 {
            border-radius: 5px;
            height: 63px;
            font-size: 25px;
            width: 107px;
            background-image: linear-gradient(to bottom, #ffebd5, #ffca92, #fe9624);
            color: #83342f;
            font-weight: bold;
            box-shadow: 0px 5px #ed9d67;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .my-btn1:hover {
            transform: scale(1.1);
        }

        .page2 .timer-item .button-items .start-button-container1 .my-btn2 {
            border-radius: 5px;
            height: 63px;
            width: 180px;
            font-size: 25px;
            background-image: linear-gradient(to bottom, #ffebd5, #ffca92, #fe9624);
            color: #83342f;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0px 5px #ed9d67;
            transition: all 0.3s ease;
        }

        .my-btn2:hover {
            transform: scale(1.1);
        }

        .my-btn3 {
            line-height: 0px;
            position: absolute;
            border-radius: 45px;
            height: 67px;
            font-size: 34px;
            width: 100%;
            background-image: linear-gradient(to bottom right, #ff7a00, #ff5b00, #d20000, #c10000);
            color: #fffdfd;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease
        }

        .my-btn3:hover {
            transform: scale(1.1);
        }

        /* 禁用状态的按钮样式 */
        .my-btn.disabled,
        .my-btn:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .my-btn.disabled:hover,
        .my-btn:disabled:hover {
            transform: none !important;
        }

        /* 确保禁用状态下的按钮不会响应点击 */
        button[disabled] {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .page2 .raffle-title {
            position: relative;
            height: 100%;
            width: 100%;
        }

        .page2 .epicycle-title {
            transform: scaleX(0.555);
            transform-origin: left;
            overflow: hidden;
            position: absolute;
            top: 28.5%;
            left: 23%;
            width: 32.5%;
        }

        .page2 .next-roud-title {
            transform: scaleX(0.555);
            transform-origin: left;
            overflow: hidden;
            position: absolute;
            top: 28.5%;
            left: 72%;
            width: 32.5%;
        }

        .page2 .people-info {
            width: 16%;
            height: 100%;
            border: 2px solid #ea3b27;
            border-radius: 15px;
            box-shadow: inset 0 0 60px #ffd5d8;
        }

        .people-info-inner {
            flex-wrap: wrap;
            display: flex;
            height: 100%;
            width: 527px;
            transform: scaleX(0.555);
            transform-origin: left;
            padding-left: 3.5%;
            padding-right: 3.5%;
        }

        .page2 .title {
            margin-left: 10px;
            width: 24%;
            height: 100%;
            border: 2px solid #ea3b27;
            border-radius: 15px;
            box-shadow: inset 0 0 60px #ffd5d8;
        }

        .page2 .title .result-title {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            font-size: 48px;
            font-weight: bold;
            color: #2c3e50;
        }

        .spicial-text {
            color: #e3223b;
        }

        .this-result-title {
            height: 270px;
            font-size: 46px;
            font-weight: bold;
            line-height: 1.2;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        /* 本轮题目 - 垂直滚动显示 */
        .this-title {
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.2;
            overflow: hidden;
        }

        .this-title .scroll-content {
            /* 动画时长将通过JavaScript动态设置 */
            width: 100%;
        }

        /* 基础垂直滚动动画 - 将通过JavaScript动态生成 */
        @keyframes scroll-vertical-base {
            0% {
                transform: translateY(0);
            }

            100% {
                transform: translateY(-100%);
            }
        }

        .timer-item {
            margin-left: 10px;
            width: 22%;
            height: 100%;
            border: 2px solid #ea3b27;
            border-radius: 15px;
            box-shadow: inset 0 0 60px #ffd5d8;
        }

        .timer-item-inner {
            padding-top: 30px;
            width: 721px;
            padding-right: 3.5%;
            padding-left: 3.5%;
            transform: scaleX(0.555);
            transform-origin: left;
            height: 100%;
            position: relative;
        }

        .next-turn-item {
            margin-left: 20px;
            width: 32%;
            height: 100%;
            border: 2px solid #8baed6;
            border-radius: 15px;
            box-shadow: inset 0px 30px 18px -11px #b9d3f1;
            background-color: #e7f0fa;
        }

        .next-turn-item-inner {
            padding-top: 30px;
            width: 1061px;
            padding-right: 3.5%;
            padding-left: 3.5%;
            transform: scaleX(0.555);
            transform-origin: left;
            height: 90%;
            position: relative;
        }

        .timer-title {
            padding-top: 4%;
            font-size: 39px;
            text-align: center;
        }

        .result-show {
            font-size: 41px;
        }

        .first-show {
            align-items: center;
            height: 100%;
            display: flex;
            justify-content: center;
        }

        .next-result-item {
            font-weight: bold;
            font-size: 53px;
            display: flex;
        }

        .next-result-people {
            font-size: 48px;
            border-radius: 20px;
            margin-right: 48px;
            margin-left: 48px;
            color: #ffffff;
            display: flex;
            justify-content: center;
            background-color: #4675b0;
            font-weight: 500;
        }

        .next-item {
            padding-bottom: 10px;
            padding-top: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }

        .next-result-title {
            font-size: 44px;
            padding-top: 20px;
            padding-left: 40px;
            padding-right: 40px;
            font-weight: bold;
            height: 240px;
            line-height: 1.2;
            overflow: hidden;
            position: relative;
        }

        /* 下一轮题目 - 垂直滚动显示 */
        .next-title {
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.3;
            height: 200px;
            /* 保持200px高度 */
            overflow: hidden;
            display: block;
            /* 默认block布局，JavaScript会动态调整 */
            width: 100%;
        }

        .next-title .scroll-content {
            /* 动画时长将通过JavaScript动态设置 */
            width: 100%;
        }

        /* 通用滚动内容样式 */
        .scroll-content {
            width: 100%;
            transition: transform 0.1s ease-out;
        }

        /* 垂直居中显示时的样式 */
        .scroll-content.centered {
            width: 100%;
        }



        /* 中间背景容器 - 继续往下移动 */
        .result-container {
            position: absolute;
            top: 40vh;
            left: 50%;
            transform: translateX(-50%);
            width: 900px;
            height: 500px;
            background: url('/static/img/index3/中间背景.png') no-repeat center center;
            background-size: contain;
            z-index: 5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 抽取结果显示区域 - 按设计图调整 */
        .result-content {
            width: 700px;
            height: 350px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            gap: 30px;
            padding-left: 80px;
            padding-top: 40px;
        }

        /* 结果项样式 - 按设计图调整 */
        .result-item {
            display: flex;
            align-items: center;
            font-size: 40px;
            font-weight: bold;
            color: #2c3e50;
        }

        /* 滚动状态下的闪烁效果 - 只应用于文字值部分 */
        .result-show.rolling .next-title {
            animation: rolling-flash 0.2s infinite alternate;
        }

        /* 滚动状态下的闪烁效果 - 只应用于文字值部分 */
        .result-show .rolling .next-item {
            animation: rolling-flash 0.2s infinite alternate;
        }

        @keyframes rolling-flash {
            0% {
                transform: scale(1);
            }

            100% {
                transform: scale(1.02);
            }
        }

        .result-icon {
            width: 80px;
            height: 80px;
            margin-right: 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            flex-shrink: 0;
        }

        .result-label {
            font-size: 41px;
            font-weight: normal;
            color: #34495e;
            min-width: 120px;
            flex-shrink: 0;
        }

        .result-text {
            font-size: 50px;
            text-align: center;
            line-height: 1.2;
            font-weight: bold;
        }

        .result-text1 {
            font-size: 60px;
            font-weight: bold;
            text-align: left;
            line-height: 1.2;
        }

        /* 图标样式 */
        .icon-name {
            background-image: url('/static/img/index2/姓名图标.png');
        }

        .icon-department {
            background-image: url('/static/img/index2/部门图标.png');
        }

        .icon-position {
            background-image: url('/static/img/index2/职位图标.png');
        }

        /* 倒计时容器 */
        .countdown-timer {
            justify-content: center;
            padding-top: 4%;
        }

        /* 确保倒计时器可见 */
        .countdown-timer[style*="flex"] {
            display: flex !important;
        }

        /* 倒计时显示容器 */
        .countdown-display {
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* 分钟和秒钟背景容器 */
        .time-unit {
            box-shadow: inset 0px 5px 10px 0px #c60000;
            border: 1px solid #facba2;
            background-image: linear-gradient(to bottom, #ff0000, #b71804);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 160px;
            height: 170px;
        }

        /* 分钟和秒钟数字 */
        .time-number {
            font-size: 90px;
            color: #dc3545;
            text-align: center;
            font-weight: 500;
            line-height: 1;

            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            -webkit-text-fill-color: transparent;
            /* 兼容旧版浏览器 */
            /* 定义渐变背景（可自定义颜色和方向） */
            background-image: linear-gradient(to bottom, #fff, #ffead4, #fac69b);
        }

        /* 分隔符（冒号） */
        .time-separator {
            font-size: 77px;
            color: #dc3545;
            font-weight: 500;
            line-height: 1;
        }

        /* 倒计时警告状态（最后5秒） */
        .countdown-warning .time-number,
        .countdown-warning .time-separator {
            color: #ff0000;
            animation: pulse-warning 1s infinite;
        }

        @keyframes pulse-warning {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.2);
            }

            100% {
                transform: scale(1);
            }
        }

        /* 倒计时结束状态 */
        .countdown-finished .time-number,
        .countdown-finished .time-separator {
            color: #ff0000;
            animation: blink-end 0.5s infinite;
        }

        @keyframes blink-end {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.3);
            }

            100% {
                transform: scale(1);
            }
        }



        /* 右上角按钮容器 - 相对于中间背景定位 */
        .top-right-buttons {
            position: absolute;
            top: 35vh;
            right: calc(50% - 450px);
            display: flex;
            gap: 15px;
            z-index: 15;
        }

        /* 右上角按钮基础样式 */
        .top-button {
            width: 150px;
            height: 60px;
            cursor: pointer;
            transition: transform 0.2s ease, opacity 0.2s ease;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .top-button:hover {
            transform: scale(1.05);
        }

        .top-button:active {
            transform: scale(0.95);
        }

        /* 抽取中时按钮禁用状态 */
        .top-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .top-button.disabled:hover {
            transform: scale(1);
        }

        /* 暂停/继续计时按钮 - 右上角 */
        .pause-timer-btn {
            background-image: url('/static/img/index3/开始计时01.png');
        }

        .pause-timer-btn:hover {
            background-image: url('/static/img/index3/开始计时02.png');
        }

        /* 暂停状态的按钮样式 */
        .pause-timer-btn.paused {
            background-image: url('/static/img/index3/开始计时02.png');
        }

        .pause-timer-btn.paused:hover {
            background-image: url('/static/img/index3/开始计时01.png');
        }

        /* 重置时间按钮 - 右上角 */
        .reset-timer-btn {
            background-image: url('/static/img/index3/开始计时01.png');
        }

        .reset-timer-btn:hover {
            background-image: url('/static/img/index3/开始计时02.png');
        }

        /* 结束按钮 - 右上角 */
        .end-btn {
            background-image: url('/static/img/index3/重置按钮01.png');
        }

        .end-btn:hover {
            background-image: url('/static/img/index3/重置按钮02.png');
        }

        /* 版权信息模块 */
        .copyright-footer {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            color: #666666;
            font-size: 32px;
            text-align: center;
            z-index: 20;
            opacity: 0.8;
            letter-spacing: 1px;
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }

        /* 答题时间到弹框样式 */
        .time-up-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            z-index: 1001;
        }

        .modal-background {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-bg-image {
            max-width: 90vw;
            max-height: 90vh;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        /* 关闭按钮 */
        .modal-close-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            width: 40px;
            height: 40px;
            background-color: rgba(220, 53, 69, 0.8);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .modal-close-btn:hover {
            background-color: rgba(220, 53, 69, 1);
            transform: scale(1.1);
        }



        /* 弹框动画 */
        .time-up-modal.show {
            animation: modalFadeIn 0.3s ease-out;
        }

        .time-up-modal.hide {
            animation: modalFadeOut 0.3s ease-in;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes modalFadeOut {
            from {
                opacity: 1;
                transform: scale(1);
            }

            to {
                opacity: 0;
                transform: scale(0.8);
            }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .modal-bg-image {
                max-width: 95vw;
                max-height: 85vh;
            }

            .modal-close-btn {
                top: 15px;
                right: 20px;
                width: 35px;
                height: 35px;
                font-size: 20px;
            }
        }



        /* 全屏样式优化 */
        .fullscreen-active {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
        }

        /* 全屏状态下确保元素位置一致性 */
        /* body:-webkit-full-screen .main-title,
        body:-moz-full-screen .main-title,
        body:fullscreen .main-title {
            position: fixed;
        } */

        /* body:-webkit-full-screen .start-button-container,
        body:-moz-full-screen .start-button-container,
        body:fullscreen .start-button-container {
            position: fixed;
        } */

        /* 防止全屏时元素位置偏移 */
        /* html:-webkit-full-screen,
        html:-moz-full-screen,
        html:fullscreen {
            width: 100%;
            height: 100%;
        } */

        /* 响应式调整 */
        @media (max-width: 768px) {
            .page2 .main-title {
                width: 65vw;
                max-width: 450px;
                top: 4vh;
            }

            .result-container {
                width: 700px;
                height: 400px;
                top: 38vh;
            }

            .result-content {
                width: 550px;
                height: 280px;
                gap: 25px;
                padding-left: 60px;
                padding-top: 30px;
            }

            .countdown-timer {
                width: 220px;
                height: 110px;
                bottom: 15px;
                right: 45px;
                gap: 15px;
            }

            .time-unit {
                width: 90px;
                height: 90px;
            }

            .time-number {
                font-size: 36px;
            }

            .time-separator {
                font-size: 32px;
                margin: 0 3px;
            }

            .result-item {
                font-size: 32px;
            }

            .result-label {
                font-size: 26px;
                min-width: 80px;
                margin-right: 10px;
            }

            .result-icon {
                width: 60px;
                height: 60px;
                margin-right: 20px;
            }

            .top-right-buttons {
                top: 33vh;
                right: calc(50% - 350px);
                gap: 12px;
            }

            .top-button {
                width: 120px;
                height: 50px;
            }

            .copyright-footer {
                font-size: 12px;
                bottom: 8px;
                letter-spacing: 0.5px;
            }
        }

        @media (max-height: 600px) {
            .page2 .main-title {
                width: 60vw;
                max-width: 400px;
                top: 3vh;
            }

            .result-container {
                width: 600px;
                height: 320px;
                top: 32vh;
            }

            .result-content {
                width: 480px;
                height: 240px;
                gap: 20px;
                padding-left: 50px;
                padding-top: 25px;
            }

            .countdown-timer {
                width: 180px;
                height: 80px;
                bottom: 10px;
                right: 35px;
                gap: 10px;
            }

            .time-unit {
                width: 70px;
                height: 70px;
            }

            .time-number {
                font-size: 28px;
            }

            .time-separator {
                font-size: 24px;
                margin: 0 2px;
            }

            .result-item {
                font-size: 28px;
            }

            .result-label {
                font-size: 22px;
                min-width: 70px;
                margin-right: 8px;
            }

            .result-icon {
                width: 50px;
                height: 50px;
                margin-right: 15px;
            }

            .top-right-buttons {
                top: 27vh;
                right: calc(50% - 250px);
                gap: 10px;
            }

            .top-button {
                width: 100px;
                height: 40px;
            }

            .copyright-footer {
                font-size: 11px;
                bottom: 5px;
                letter-spacing: 0.3px;
            }
        }

        .custom-layer {
            border-radius: 20px;
            /* width: 1434px;
            height: 671px; */
            z-index: 1;
            position: fixed;
        }

        .layui-layer-page .layui-layer-content {
            height: 100%;
        }

        /* 自定义标题栏整体样式 */
        .custom-layer .layui-layer-title {
            background-image: linear-gradient(to right, #ea0d00, #e20000, #e40000);
            height: 8%;
            padding: 12px 20px;
            border-radius: 20px 20px 0 0;
        }

        /* 自定义关闭按钮 */
        .custom-layer .layui-layer-setwin a {
            /* 关闭按钮大小和位置 */
            width: 30px;
            height: 30px;
            line-height: 30px;
            margin-right: 10px;
            margin-top: 5px;

            /* 关闭按钮样式 */
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 50%;
            text-align: center;
            font-size: 16px;

            /* 过渡动画 */
            transition: all 0.3s;
        }

        /* 关闭按钮 hover 效果 */
        .custom-layer .layui-layer-setwin a:hover {
            background-color: #e74c3c;
            transform: rotate(90deg);
        }

        /* 隐藏默认关闭按钮的文字（只保留X） */
        .custom-layer .layui-layer-setwin a::after {
            content: '×';
        }

        .title-inner {
            height: 93%;
            padding: 3.5%;
            display: flex;
            align-items: center;
            justify-items: center;
            width: 785px;
            transform: scaleX(0.555);
            transform-origin: left;
        }
    </style>
</head>

<body tabindex="0">
    <!-- 第1页 -->
    <div class="page1" id="page1">
    </div>

    <!-- 第2页 -->
    <div class="page2" id="page2">

        <div class="center-class">
            <!-- 中间背景和抽取结果 -->
            <img src="/static/img/index2/抽奖背景.png" class="raffle-title" alt="抽奖背景">
            <!-- 本轮标题 -->
            <img src="/static/img/index2/本轮答题标题.png" class="epicycle-title" alt="本轮标题"></img>
            <!-- 下一轮标题 -->
            <img src="/static/img/index2/下一轮答题.png" class="next-roud-title" alt="下一轮标题"></img>

            <div class="center-base">

                <!-- 人员信息 -->
                <div class="people-info">
                    <div class="people-info-inner">
                        <div class="result-item">
                            <div class="result-icon icon-name"></div>
                            <div class="result-label">姓名：</div>
                            <div class="result-text1" id="thisName">待抽取</div>
                        </div>
                        <div class="result-item">
                            <div class="result-icon icon-department"></div>
                            <div class="result-label">机构：</div>
                            <div class="result-text" id="thisDept">待抽取</div>
                        </div>
                        <div class="result-item">
                            <div class="result-icon icon-position"></div>
                            <div class="result-label">职务：</div>
                            <div class="result-text" id="thisPosition">待抽取</div>
                        </div>
                    </div>

                </div>
                <!--题目 -->
                <div class="title">
                    <div class="title-inner">
                        <div class="result-title">
                            <div class="result-text" id="resultQuestion">
                                请点击
                                <span class="spicial-text">
                                    “开始抽题”
                                </span>
                                按钮抽题
                            </div>
                        </div>
                        <div class="this-result-title" style="display: none;">
                            <span class="this-title"
                                id="thisQuestion">收单风险是指特约商户、犯罪分子或商户与犯罪分子合谋，利用商户经营场所和经营设施，通过商户POS交易进行套现、洗单、测录、欺诈、违规移机、恶意倒闭等行为给收单机构及持卡人带来的财务损失及商誉损失的风险。</span>
                        </div>
                    </div>
                </div>
                <!-- 计时器 -->
                <div class="timer-item">
                    <div class="timer-item-inner">
                        <div class="timer-title">
                            距离答题结束还剩
                        </div>
                        <div class="countdown-timer" id="countdownTimer">
                            <div class="countdown-display" id="countdownDisplay">
                                <div class="time-unit">
                                    <span class="time-number" id="minutes">10</span>
                                </div>
                                <span class="time-separator">:</span>
                                <div class="time-unit">
                                    <span class="time-number" id="seconds">00</span>
                                </div>
                            </div>
                        </div>
                        <!-- 按钮 -->
                        <div class="button-items">
                            <div class="start-button-container1">
                                <button type="button" class="layui-btn my-btn1" onclick="startTimer()">开始</button>
                            </div>
                            <div class="start-button-container1">
                                <button type="button" class="layui-btn my-btn2" onclick="toggleTimer()">暂停/继续</button>
                            </div>
                            <div class="start-button-container1">
                                <button type="button" class="layui-btn my-btn1" onclick="changeAnswer()">切换</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 下一轮 -->
                <div class="next-turn-item">
                    <div class="next-turn-item-inner">
                        <!-- 首次展示 -->
                        <div class="first-show">
                            <div class="next-result-item" id="nextResultQuestion">
                                请点击
                                <span class="spicial-text">
                                    “开始抽题”
                                </span>
                                按钮抽题
                            </div>
                        </div>
                        <!-- 抽到人之后展示 -->
                        <div class="result-show" style="display: none;">
                            <div class="next-result-people">
                                <span class="next-item" id="nextName">李思思</span>
                                <span class="next-item" id="nextDept">金融科技部</span>
                                <span class="next-item" id="nextPosition">科技经理</span>
                            </div>
                            <div class="next-result-title">
                                <span class="next-title" id="nextQuestion">如何识别房企客户的现金流风险？</span>
                            </div>
                        </div>
                        <div class="start-button-container">
                            <button type="button" class="layui-btn layui-btn-radius my-btn" id="mainActionButton"
                                onclick="startExtraction()">开始抽题</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 版权信息 -->
        <!-- <div class="copyright-footer">
            金融科技部版权所有
        </div> -->
    </div>

    <script>
        // 本次抽取次数
        let currentTimes = 1;
        let currentPage = 1;
        let isExtracting = false;
        let countdownInterval = null;
        let totalSeconds = 480; // 10分钟 = 600秒
        let isCountdownRunning = false;
        let isCountdownPaused = false;
        let extractionInterval = null;
        let extractionSoundInterval = null;
        let extractionTimeout = null; // 用于跟踪6秒定时器
        let allPersons = [];
        let allQuestions = [];
        let extractionStartTime = null;
        let dialogClose = true;

        // 滚动动画控制器
        let scrollAnimationControllers = new Map(); // 存储各个元素的滚动控制器
        let timeOverText = `<div style="transform-origin: left;transform: scaleX(0.555);width: 1549px;height: 100%;position: relative;">
                                            <img src='/static/img/index2/插画.png' style="position: absolute;top: 5%;left: 43%;height: 43%;">
                                            </img>
                                            <div style="position: absolute;top: 54%;left: 46%;width: 22%;height: 18%;font-size: 49px;font-weight: 500;">
                                                答题结束
                                            </div>
                                            <div style="position: absolute;top: 77%;left: 42.5%;width: 21%;height: 15%;">
                                                <button type="button" class="layui-btn my-btn3" onclick="closeDialog()">关闭</button>
                                            </div>
                                        </div>`
        // 是否是第一次抽题（需要准备时间）
        let isFirstExtraction = true;
        // 准备时间（5分钟 = 300秒）
        let preparationTime = 300;
        // 答题时间（10分钟 = 600秒）
        let answerTime = 65;
        // 当前是否处于准备阶段
        let isPreparationPhase = false;

        // 题目显示配置 - 基于人类阅读速度优化
        const QUESTION_CONFIG = {
            threshold: 30,           // 题目长度阈值，超过此长度启用垂直滚动
            charsPerLine: 11,       // 每行平均字符数（10-12字符的中间值）
            readTimePerLine: 3.5,   // 每行阅读时间（秒）- 理解性阅读
            initialPause: 4,        // 开头停留时间（秒）- 让用户准备阅读
            finalPause: 2,          // 结尾停留时间（秒）- 优化为2秒
            minTime: 12,            // 最小滚动时间（秒）
            maxTime: 45,            // 最大滚动时间（秒）- 避免过长等待
            scrollBuffer: 1.2,      // 滚动缓冲系数 - 给眼睛追踪留余量
            restartDelay: 2         // 滚动结束后重新开始的等待时间（秒）
        };
        // 基于人类阅读速度计算滚动时间
        function calculateScrollTime(textLength, containerHeight) {
            const config = QUESTION_CONFIG;

            // 计算行数（向上取整）
            const lineCount = Math.ceil(textLength / config.charsPerLine);

            // 计算纯阅读时间（行数 × 每行阅读时间）
            const readingTime = lineCount * config.readTimePerLine;

            // 计算滚动时间（阅读时间 × 缓冲系数）
            const scrollTime = readingTime * config.scrollBuffer;

            // 总时间 = 开头停留 + 滚动时间 + 结尾停留
            let totalTime = config.initialPause + scrollTime + config.finalPause;

            // 限制在合理范围内
            totalTime = Math.max(config.minTime, Math.min(config.maxTime, totalTime));

            return {
                totalTime: totalTime,
                scrollTime: scrollTime,
                lineCount: lineCount,
                readingTime: readingTime,
                containerHeight: containerHeight
            };
        }

        // 计算智能滚动距离 - 确保完整行显示
        function calculateSmartScrollDistance(element, scrollContent) {
            const containerHeight = element.offsetHeight;
            const contentHeight = scrollContent.offsetHeight;

            if (contentHeight <= containerHeight) {
                return 0; // 不需要滚动
            }

            // 获取字体信息，优先从scrollContent获取，如果没有则从element获取
            let computedStyle = window.getComputedStyle(scrollContent);
            let fontSize = parseFloat(computedStyle.fontSize);
            let lineHeight = parseFloat(computedStyle.lineHeight);

            // 如果scrollContent没有明确的样式，从父元素获取
            if (!fontSize || fontSize === 0) {
                computedStyle = window.getComputedStyle(element);
                fontSize = parseFloat(computedStyle.fontSize);
                lineHeight = parseFloat(computedStyle.lineHeight);
            }

            // 如果lineHeight是normal或无效值，使用默认计算
            if (!lineHeight || lineHeight === 0 || computedStyle.lineHeight === 'normal') {
                lineHeight = fontSize * 1.3; // 默认行高系数
            }

            // 计算可以完整显示的行数
            const visibleLines = Math.floor(containerHeight / lineHeight);
            const totalLines = Math.ceil(contentHeight / lineHeight);

            if (totalLines <= visibleLines) {
                return 0; // 所有行都可见
            }

            // 计算需要滚动的距离，确保最后一行完整显示
            const hiddenLines = totalLines - visibleLines;
            const scrollDistance = hiddenLines * lineHeight;

            return scrollDistance;
        }

        // 检查文本内容是否需要滚动
        function needsScrolling(element, text) {
            if (!element || !text) return false;

            try {
                // 获取容器尺寸
                let containerWidth = element.offsetWidth;
                let containerHeight = element.offsetHeight;

                // 如果元素尺寸为0，尝试从父容器获取
                if (containerWidth === 0 || containerHeight === 0) {
                    const parent = element.parentElement;
                    if (parent) {
                        containerWidth = parent.offsetWidth * 0.89; // 考虑width: 89%
                        containerHeight = parent.offsetHeight;
                    }
                }

                // 如果还是0，使用默认值
                if (containerWidth === 0 || containerHeight === 0) {
                    containerWidth = 600; // 默认宽度
                    containerHeight = 455; // 默认高度（来自CSS）
                }

                // 创建临时元素来测量文本高度
                const tempElement = document.createElement('div');
                const computedStyle = window.getComputedStyle(element);

                // 安全地设置样式
                tempElement.style.position = 'absolute';
                tempElement.style.visibility = 'hidden';
                tempElement.style.width = containerWidth + 'px';
                tempElement.style.fontSize = computedStyle.fontSize || '66px';
                tempElement.style.fontWeight = computedStyle.fontWeight || 'bold';
                tempElement.style.lineHeight = computedStyle.lineHeight || '1.3';
                tempElement.style.whiteSpace = 'normal';
                tempElement.style.wordWrap = 'break-word';

                tempElement.textContent = text;
                document.body.appendChild(tempElement);

                const textHeight = tempElement.offsetHeight;

                document.body.removeChild(tempElement);

                const needsScroll = textHeight > containerHeight;

                return needsScroll;
            } catch (error) {
                console.warn('检查滚动需求时出错:', error);
                return text.length > QUESTION_CONFIG.threshold;
            }
        }



        // 处理题目垂直滚动显示 - 智能优化版本
        function handleQuestionDisplay(element, text) {
            if (!element || !text) {
                console.warn('handleQuestionDisplay: 无效的元素或文本', element, text);
                return;
            }

            // 重置元素样式为默认状态
            element.style.display = 'block';
            element.style.alignItems = '';
            element.style.justifyContent = '';
            if (element.tagName.toLowerCase() === 'span') {
                element.style.width = '100%';
                element.style.height = '100%';
            }

            // 清空元素内容
            element.innerHTML = '';

            // 移除之前的动画样式
            try {
                const oldStyles = document.querySelectorAll('style[data-scroll-animation]');
                oldStyles.forEach(style => style.remove());
            } catch (error) {
                console.warn('清理旧样式时出错:', error);
            }

            // 创建内容容器
            const scrollContent = document.createElement('div');
            scrollContent.className = 'scroll-content';
            scrollContent.textContent = text;
            scrollContent.style.transition = 'transform 0.1s linear';
            element.appendChild(scrollContent);

            // 使用智能滚动控制
            setTimeout(() => {
                try {
                    // 再次检查是否需要滚动（基于实际渲染后的尺寸）
                    const actualContainerHeight = element.offsetHeight;
                    const actualContentHeight = scrollContent.offsetHeight;
                    const actualNeedsScroll = actualContentHeight > actualContainerHeight;

                    if (actualNeedsScroll) {
                        // 需要滚动：移除居中样式，启用滚动动画
                        element.style.display = 'block';
                        element.style.alignItems = '';
                        element.style.justifyContent = '';
                        scrollContent.classList.remove('centered');
                        scrollContent.style.transform = 'translateY(0)';
                        createSmartScrollAnimation(element, text);
                    } else {
                        // 不需要滚动：设置为flex布局，内容垂直居中显示
                        element.style.display = 'flex';
                        element.style.alignItems = 'center';
                        element.style.justifyContent = ''; // 不设置水平居中
                        scrollContent.classList.add('centered');
                        scrollContent.style.transform = 'translateY(0)';
                    }
                } catch (error) {
                    console.error('创建滚动动画时出错:', error);
                    // 降级处理：直接显示文本
                    scrollContent.style.transform = 'translateY(0)';
                    scrollContent.style.animation = 'none';
                }
            }, 100); // 短暂延迟确保DOM更新完成
        }

        // 智能滚动控制 - 检测内容显示完毕后的处理
        function createSmartScrollAnimation(element, text) {
            if (!element || !text) {
                console.warn('createSmartScrollAnimation: 无效的元素或文本');
                return;
            }

            try {
                // 停止之前的动画控制器
                const elementId = element.id || element.className || 'unknown';

                if (scrollAnimationControllers.has(elementId)) {
                    const oldController = scrollAnimationControllers.get(elementId);
                    if (oldController && typeof oldController.stop === 'function') {
                        oldController.stop();
                    }
                }

                const needsScroll = needsScrolling(element, text) && text.length > QUESTION_CONFIG.threshold;

                if (!needsScroll) {
                    // 不需要滚动，直接显示
                    const scrollContent = element.querySelector('.scroll-content');
                    if (scrollContent) {
                        scrollContent.style.transform = 'translateY(0)';
                        scrollContent.style.animation = 'none';
                    }
                    return;
                }

                const config = QUESTION_CONFIG;
                const timeData = calculateScrollTime(text.length, element.offsetHeight);
                const scrollContent = element.querySelector('.scroll-content');

                if (!scrollContent) {
                    return;
                }

                // 等待一帧确保元素渲染完成，然后计算智能滚动距离
                let scrollDistance;

                function calculateScrollDistance() {
                    scrollDistance = calculateSmartScrollDistance(element, scrollContent);

                    if (scrollDistance <= 0) {
                        return false;
                    }
                    return true;
                }

                // 初始计算
                if (!calculateScrollDistance()) {
                    return;
                }

                // 创建滚动控制器
                let scrollPhase = 'initial'; // initial -> scrolling -> waiting -> restart
                let phaseStartTime = Date.now();
                let animationId = null;
                let isRunning = true;

                function updateScrollAnimation() {
                    if (!isRunning) return;

                    const currentTime = Date.now();
                    const elapsedTime = (currentTime - phaseStartTime) / 1000;

                    switch (scrollPhase) {
                        case 'initial':
                            // 初始停留阶段
                            scrollContent.style.transform = 'translateY(0)';
                            if (elapsedTime >= config.initialPause) {
                                // 重新计算滚动距离（确保准确性）
                                if (calculateScrollDistance()) {
                                    scrollPhase = 'scrolling';
                                    phaseStartTime = currentTime;
                                } else {
                                    isRunning = false;
                                    return;
                                }
                            }
                            break;

                        case 'scrolling':
                            // 滚动阶段 - 基于实际内容高度计算
                            const scrollProgress = Math.min(elapsedTime / timeData.scrollTime, 1);
                            const currentScrollDistance = scrollProgress * scrollDistance;
                            const translateYPx = -currentScrollDistance;
                            scrollContent.style.transform = `translateY(${translateYPx}px)`;

                            // 每秒输出一次进度（避免过多日志）
                            if (Math.floor(elapsedTime) !== Math.floor(elapsedTime - 0.1)) {
                            }

                            if (scrollProgress >= 1) {
                                scrollPhase = 'waiting';
                                phaseStartTime = currentTime;
                            }
                            break;

                        case 'waiting':
                            // 等待阶段（内容已完全显示，保持在最终位置）
                            scrollContent.style.transform = `translateY(-${scrollDistance}px)`;
                            if (elapsedTime >= config.restartDelay) {
                                scrollPhase = 'initial';
                                phaseStartTime = currentTime;
                            }
                            break;
                    }

                    // 继续动画循环
                    if (isRunning) {
                        animationId = requestAnimationFrame(updateScrollAnimation);
                    }
                }

                // 创建控制器对象
                const controller = {
                    stop: function () {
                        isRunning = false;
                        if (animationId) {
                            cancelAnimationFrame(animationId);
                            animationId = null;
                        }
                    },
                    restart: function () {
                        if (!isRunning) {
                            isRunning = true;
                            scrollPhase = 'initial';
                            phaseStartTime = Date.now();
                            updateScrollAnimation();
                        }
                    }
                };

                // 存储控制器
                scrollAnimationControllers.set(elementId, controller);

                // 开始动画
                updateScrollAnimation();

            } catch (error) {
                // 降级处理：直接显示文本
                const scrollContent = element.querySelector('.scroll-content');
                if (scrollContent) {
                    scrollContent.style.transform = 'translateY(0)';
                    scrollContent.style.animation = 'none';
                }
            }
        }

        // 测试本轮题目滚动的专用函数
        function testThisQuestionScroll() {
            const element = document.getElementById('thisQuestion');
            // 使用更长的文本来测试本轮题目的滚动，确保最后一行可能被截断
            const testText = "这是一个很长的测试题目，专门用来验证本轮答题模块的智能滚动功能是否正常工作。这个题目的内容需要足够长，以便能够产生多行";

            if (element) {
                // 先显示容器
                const container = document.querySelector('.this-result-title');
                if (container) {
                    container.style.display = 'block';

                    setTimeout(() => {
                        handleQuestionDisplay(element, testText);
                    }, 100);
                }
            }
        }

        // 测试下一轮题目滚动的专用函数
        function testNextQuestionScroll() {
            const element = document.getElementById('nextQuestion');
            const testText = "这是一个测试下一轮题目滚动的长文本内容。这个文本应该刚好三行左右，用来测试当第三行不能完全显示时，滚动是否能让第三行完整显示出来。";

            if (element) {
                // 先显示容器
                document.querySelector('.first-show').style.display = 'none';
                document.querySelector('.result-show').style.display = 'block';

                setTimeout(() => {
                    handleQuestionDisplay(element, testText);
                }, 100);
            }
        }

        // 页面加载完成后设置焦点和全屏，以便接收键盘事件
        window.onload = function () {
            document.body.focus();
            requestFullscreen();
            checkTaskStatus();

            // 添加测试按钮（临时调试用）- 已隐藏
            /*
            setTimeout(() => {
                const testBtn1 = document.createElement('button');
                testBtn1.textContent = '测试本轮滚动';
                testBtn1.style.position = 'fixed';
                testBtn1.style.top = '10px';
                testBtn1.style.right = '10px';
                testBtn1.style.zIndex = '9999';
                testBtn1.style.marginRight = '10px';
                testBtn1.onclick = testThisQuestionScroll;
                document.body.appendChild(testBtn1);

                const testBtn2 = document.createElement('button');
                testBtn2.textContent = '测试下一轮滚动';
                testBtn2.style.position = 'fixed';
                testBtn2.style.top = '10px';
                testBtn2.style.right = '150px';
                testBtn2.style.zIndex = '9999';
                testBtn2.onclick = testNextQuestionScroll;
                document.body.appendChild(testBtn2);
            }, 1000);
            */
        };
        // 1. 初始化Layui并暴露layer到全局
        let layer;
        layui.use('layer', function () {
            layer = layui.layer; // 将layer对象全局化
        });
        // 调用自定义页面层
        function openCustomLayer(customContent) {
            // 确保layer已加载
            if (!layer) {
                alert('Layui模块加载中，请稍后');
                return;
            }

            // 调用页面层
            layer.open({
                type: 1,
                title: ['','background-image: linear-gradient(to right #ea0d00,#e20000,#e40000);']
                ,
                // 添加自定义类名到弹框容器
                skin: 'custom-layer',
                shadeClose: false,
                closeBtn: 0,
                offset: ['18%', '27%'],
                area: ['45%', '67%'],
                content: customContent,
                // 隐藏默认最大化/最小化按钮（只保留关闭按钮）
                maxmin: false
            });
            dialogClose = false;
        }

        // 关闭弹窗
        function closeDialog() {
            dialogClose = true;
            layer.close(layer.index);
        }

        // 检查任务状态
        async function checkTaskStatus() {
            try {
                const quotaCheck = await checkQuotaAvailable();
                if (!quotaCheck.available) {
                    let customContent = `<div style="width: 100%;height: 100%;position: relative;display: flex;align-items: center;justify-content: center;">
                                            <div style="transform: scaleX(0.555);font-size: 50px;font-weight: 500;">
                                                <span>${quotaCheck.message}</span>
                                            </div>
                                        </div>`
                    openCustomLayer(customContent)
                    if (!quotaCheck.available) {
                        return;
                    }
                    return;
                }
            } catch (error) {
                console.warn('任务状态检查失败：', error);
            }
        }

        function changeAnswer() {
            // 检查是否正在抽题，如果是则不允许切换
            if (isExtracting) {
                return;
            }

            // 检查下一轮是否有抽题结果
            const nextName = document.getElementById('nextName').textContent;
            if (nextName === '正在抽取...' || nextName === '李思思') {
                // 如果下一轮没有抽题结果，不允许切换
                return;
            }

            // 获取下一轮抽题的结果
            const nextDept = document.getElementById('nextDept').textContent;
            const nextPosition = document.getElementById('nextPosition').textContent;
            const nextQuestion = document.getElementById('nextQuestion').textContent;

            // 更新本轮显示区域的内容
            document.getElementById('thisName').textContent = nextName;
            document.getElementById('thisDept').textContent = nextDept;
            document.getElementById('thisPosition').textContent = nextPosition;

            // 先显示本轮题目区域，隐藏提示文字
            document.querySelector('.result-title').style.display = 'none';
            document.querySelector('.this-result-title').style.display = 'block';

            // 然后处理题目显示样式（此时元素已经可见，可以正确测量尺寸）
            const thisQuestionElement = document.getElementById('thisQuestion');
            // 使用setTimeout确保DOM更新完成
            setTimeout(() => {
                handleQuestionDisplay(thisQuestionElement, nextQuestion);
            }, 50);

            // 保持first-show隐藏，result-show显示
            document.querySelector('.first-show').style.display = 'none';
            document.querySelector('.result-show').style.display = 'block';

            // 更新currentTimes
            currentTimes++;

            // 如果是第一次切换（第一个人），设置准备阶段标志
            if (isFirstExtraction) {
                isFirstExtraction = false;
                isPreparationPhase = true;
                // 更新计时器标题为准备时间
                document.querySelector('.timer-title').textContent = '距离答题开始还剩';
                // 设置准备时间
                totalSeconds = preparationTime;
                updateCountdownDisplay();

                // 第一个人：开始按钮保持"开始"文字
                const startButton = document.querySelector('.timer-item .button-items .start-button-container1 .my-btn1');
                if (startButton) {
                    startButton.textContent = '开始';
                    startButton.onclick = function () {
                        startTimer();
                    };
                }
            } else {
                // 对于后续人员，直接设置为答题阶段（没有准备阶段）
                isPreparationPhase = false;
                document.querySelector('.timer-title').textContent = '距离答题结束还剩';
                totalSeconds = answerTime;
                updateCountdownDisplay();

                // 后续人员：直接显示"答题"按钮
                const startButton = document.querySelector('.timer-item .button-items .start-button-container1 .my-btn1');
                if (startButton) {
                    startButton.textContent = '答题';
                    startButton.onclick = function () {
                        startAnswerPeriod();
                    };
                }
            }

            // 重置切换按钮（对所有人都一样）
            const switchButton = document.querySelector('.timer-item .button-items .start-button-container1:nth-child(3) .my-btn1');
            if (switchButton) {
                switchButton.textContent = '切换';
                switchButton.onclick = function () {
                    changeAnswer();
                };
            }

            // 重置按钮状态为开始抽题
            setupStartExtractionButton();

            // 停止当前计时器
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
                isCountdownRunning = false;
                isCountdownPaused = false;
            }
        }

        // 请求全屏
        function requestFullscreen() {
            const docElm = document.documentElement;
            if (docElm.requestFullscreen) {
                docElm.requestFullscreen();
            } else if (docElm.mozRequestFullScreen) {
                docElm.mozRequestFullScreen();
            } else if (docElm.webkitRequestFullScreen) {
                docElm.webkitRequestFullScreen();
            } else if (docElm.msRequestFullscreen) {
                docElm.msRequestFullscreen();
            }
        }

        // 监听全屏状态变化
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);

        function handleFullscreenChange() {
            setTimeout(function () {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        }

        // 记录按键状态
        let keysPressed = {
            space: false,
            enter: false
        };

        // 全局键盘事件监听
        document.addEventListener('keydown', function (event) {
            if (dialogClose == false) {
                return;
            }

            // 记录按键状态
            if (event.code === 'Space') {
                keysPressed.space = true;
            }
            if (event.code === 'Enter') {
                keysPressed.enter = true;
            }

            if (currentPage === 1) {
                // 在页面1禁用回车键
                if (event.code === 'Enter') {
                    event.preventDefault();
                    return;
                }
                // 只允许空格键跳转到抽题页面
                if (event.code === 'Space') {
                    event.preventDefault();
                    switchTopage2();
                }
            } else if (currentPage === 3) {
                // 在抽题页面禁用单独的空格键和回车键
                if (event.code === 'Space' && !keysPressed.enter) {
                    event.preventDefault();
                    return;
                }
                if (event.code === 'Enter' && !keysPressed.space) {
                    event.preventDefault();
                    return;
                }
                // 检查是否同时按下空格键和回车键
                if (keysPressed.space && keysPressed.enter) {
                    event.preventDefault();
                    forceReturnToPage1();
                }
            }
        });

        // 监听按键释放事件
        document.addEventListener('keyup', function (event) {
            // 重置按键状态
            if (event.code === 'Space') {
                keysPressed.space = false;
            }
            if (event.code === 'Enter') {
                keysPressed.enter = false;
            }
        });

        // 空格键监听器（用于结束会话后回到页面1）
        let spaceKeyListenerEnabled = false;

        function enableSpaceKeyListener() {
            spaceKeyListenerEnabled = true;
        }

        function disableSpaceKeyListener() {
            spaceKeyListenerEnabled = false;
        }

        // 添加专门的空格键监听
        document.addEventListener('keydown', function (event) {
            if (spaceKeyListenerEnabled && event.code === 'Space') {
                event.preventDefault();
                returnToPage1();
            }
        });

        // 添加ESC键关闭弹框的监听
        document.addEventListener('keydown', function (event) {
            if (event.code === 'Escape') {
                const modal = document.getElementById('timeUpModal');
                if (modal && modal.style.display === 'flex') {
                    event.preventDefault();
                    closeTimeUpModal();
                }
            }
        });

        // 从页面2回到页面1的函数（空格键触发，有条件限制）
        function forceReturnToPage1() {
            // 检查是否正在抽取中
            if (isExtracting) {
                return;
            }

            // 检查是否正在倒计时且计时器没有归零
            if (isCountdownRunning && totalSeconds > 0) {
                return;
            }

            // 检查是否倒计时暂停但时间未归零
            if (isCountdownPaused && totalSeconds > 0) {
                return;
            }

            // 重置所有状态
            resetAllStates();

            // 回到第一页
            currentPage = 1;
            document.getElementById('page2').classList.remove('active');
            document.getElementById('page1').style.display = 'flex';

            // 恢复背景和点击事件
            // document.body.style.backgroundImage = "url('/static/img/index1/背景.jpg')";
            document.body.onclick = switchTopage2;
            document.body.style.cursor = 'pointer';
        }

        // 从页面2回到页面1的函数（保留原有的条件检查逻辑）
        function returnToPage1Frompage2() {
            // 检查是否正在抽取中
            if (isExtracting) {
                return;
            }

            // 检查是否正在倒计时且计时器没有归零
            if (isCountdownRunning && totalSeconds > 0) {
                return;
            }

            // 检查是否倒计时暂停但时间未归零
            if (isCountdownPaused && totalSeconds > 0) {
                return;
            }

            // 重置所有状态
            resetAllStates();

            // 回到第一页
            currentPage = 1;
            document.getElementById('page2').classList.remove('active');
            document.getElementById('page1').style.display = 'flex';

            // 恢复背景和点击事件
            // document.body.style.backgroundImage = "url('/static/img/index1/背景.jpg')";
            document.body.onclick = switchTopage2;
            document.body.style.cursor = 'pointer';
        }

        // 回到页面1的函数（用于结束会话后的空格键监听）
        function returnToPage1() {
            // 禁用空格键监听
            disableSpaceKeyListener();

            // 重置所有状态
            resetAllStates();

            // 回到第一页
            currentPage = 1;
            document.getElementById('page2').classList.remove('active');
            document.getElementById('page1').style.display = 'flex';

            // 恢复背景和点击事件
            // document.body.style.backgroundImage = "url('/static/img/index1/背景.jpg')";
            document.body.onclick = switchTopage2;
            document.body.style.cursor = 'pointer';
        }

        // 重置所有状态的函数
        function resetAllStates() {
            // 重置抽取状态
            isExtracting = false;

            // 停止所有定时器
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }
            if (extractionTimeout) {
                clearTimeout(extractionTimeout);
                extractionTimeout = null;
            }

            // 停止抽题音效循环
            stopExtractionSoundLoop();

            // 停止所有滚动动画
            scrollAnimationControllers.forEach((controller, elementId) => {
                controller.stop();
            });
            scrollAnimationControllers.clear();

            // 重置倒计时状态
            isCountdownRunning = false;
            isCountdownPaused = false;
            totalSeconds = 0; // 重置为0，与页面初始化一致

            // 重置其他状态变量
            isFirstExtraction = true;
            isPreparationPhase = false;
            currentTimes = 1;

            // 重置显示内容（安全检查，避免null错误）
            const resultName = document.getElementById('resultName');
            if (resultName) resultName.textContent = '准备抽取...';

            const resultDepartment = document.getElementById('resultDepartment');
            if (resultDepartment) resultDepartment.textContent = '准备抽取...';

            const resultPosition = document.getElementById('resultPosition');
            if (resultPosition) resultPosition.textContent = '准备抽取...';

            const resultQuestion = document.getElementById('resultQuestion');
            if (resultQuestion) {
                resultQuestion.innerHTML = `请点击<span class="spicial-text">"开始抽题"</span>按钮抽题`;
            }

            // 重置本轮答题模块的显示状态
            const resultTitle = document.querySelector('.result-title');
            if (resultTitle) {
                resultTitle.style.display = 'block';
            }

            const thisResultTitle = document.querySelector('.this-result-title');
            if (thisResultTitle) {
                thisResultTitle.style.display = 'none';
            }

            // 重置实际存在的元素
            const thisName = document.getElementById('thisName');
            if (thisName) thisName.textContent = '待抽取';

            const thisDept = document.getElementById('thisDept');
            if (thisDept) thisDept.textContent = '待抽取';

            const thisPosition = document.getElementById('thisPosition');
            if (thisPosition) thisPosition.textContent = '待抽取';

            const thisQuestion = document.getElementById('thisQuestion');
            if (thisQuestion) thisQuestion.textContent = '如何识别房企客户的现金流风险？';

            // 重置下一轮显示区域的状态
            const firstShow = document.querySelector('.first-show');
            if (firstShow) {
                firstShow.style.display = 'flex';
            }

            const resultShow = document.querySelector('.result-show');
            if (resultShow) {
                resultShow.style.display = 'none';
            }

            const nextName = document.getElementById('nextName');
            if (nextName) nextName.textContent = '李思思';

            const nextDept = document.getElementById('nextDept');
            if (nextDept) nextDept.textContent = '金融科技部';

            const nextPosition = document.getElementById('nextPosition');
            if (nextPosition) nextPosition.textContent = '科技经理';

            const nextQuestion = document.getElementById('nextQuestion');
            if (nextQuestion) nextQuestion.textContent = '如何识别房企客户的现金流风险？';

            // 重置倒计时显示
            updateCountdownDisplay();

            // 重置倒计时样式
            const countdownDisplay = document.getElementById('countdownDisplay');
            if (countdownDisplay) {
                countdownDisplay.classList.remove('countdown-warning', 'countdown-finished');
            }

            // 重置结果项样式
            const resultTexts = document.querySelectorAll('.result-text');
            resultTexts.forEach(text => {
                text.style.color = '#2c3e50';
                text.style.transform = 'scale(1)';
            });

            // 移除滚动效果样式
            const resultItems = document.querySelectorAll('.result-show');
            resultItems.forEach(item => {
                item.classList.remove('rolling');
            });

            // 重新启用所有按钮
            enableTimerButtons();
        }

        // 直接切换到第2页
        function switchTopage2() {
            if (currentPage !== 1) return;

            currentPage = 3;

            // 切换背景图片到页面2
            document.body.style.backgroundSize = "cover";
            document.body.style.backgroundPosition = "center center";
            document.body.style.backgroundRepeat = "no-repeat";

            // 隐藏第一页，直接显示第2页
            document.getElementById('page1').style.display = 'none';
            document.getElementById('page2').classList.add('active');

            // 移除body的点击事件
            document.body.onclick = null;
            document.body.style.cursor = 'default';

            // 显示倒计时器
            const countdownTimer = document.getElementById('countdownTimer');
            if (countdownTimer) {
                countdownTimer.style.display = 'flex';
            }

            // 初始化倒计时显示为0
            if (!isCountdownRunning && !isCountdownPaused) {
                totalSeconds = 0;
                updateCountdownDisplay();
            }

            // 设置按钮为开始抽题状态
            setupStartExtractionButton();

            // 强制重新计算布局以确保全屏兼容性
            setTimeout(function () {
                window.dispatchEvent(new Event('resize'));
            }, 50);
        }

        // 设置按钮为开始抽题状态
        function setupStartExtractionButton() {
            const mainButton = document.getElementById('mainActionButton');
            if (mainButton) {
                mainButton.classList.remove('restart-mode', 'extracting');
                mainButton.onclick = function (event) {
                    startExtraction(event);
                };
            }
        }

        // 设置按钮为重新抽题状态
        function setupRestartExtractionButton() {
            const mainButton = document.getElementById('mainActionButton');
            if (mainButton) {
                mainButton.classList.add('restart-mode');
                mainButton.classList.remove('extracting');
                mainButton.onclick = function (event) {
                    restartExtraction(event);
                };
            }
        }

        // 禁用计时器和结束按钮
        function disableTimerButtons() {
            const pauseBtn = document.getElementById('pauseTimerBtn');
            const resetBtn = document.querySelector('.reset-timer-btn');
            const endBtn = document.querySelector('.end-btn');

            // 禁用计时器区域的按钮
            const timerButtons = document.querySelectorAll('.timer-item .button-items .my-btn1, .timer-item .button-items .my-btn2');
            timerButtons.forEach(button => {
                button.classList.add('disabled');
                button.disabled = true;
            });

            if (pauseBtn) {
                pauseBtn.classList.add('disabled');
            }
            if (resetBtn) {
                resetBtn.classList.add('disabled');
            }
            if (endBtn) {
                endBtn.classList.add('disabled');
            }
        }

        // 启用计时器和结束按钮
        function enableTimerButtons() {
            const pauseBtn = document.getElementById('pauseTimerBtn');
            const resetBtn = document.querySelector('.reset-timer-btn');
            const endBtn = document.querySelector('.end-btn');

            // 启用计时器区域的按钮
            const timerButtons = document.querySelectorAll('.timer-item .button-items .my-btn1, .timer-item .button-items .my-btn2');
            timerButtons.forEach(button => {
                button.classList.remove('disabled');
                button.disabled = false;
            });

            if (pauseBtn) {
                pauseBtn.classList.remove('disabled');
            }
            if (resetBtn) {
                resetBtn.classList.remove('disabled');
            }
            if (endBtn) {
                endBtn.classList.remove('disabled');
            }
        }

        // 校验当天是否配置任务以及名额是否充足
        async function checkQuotaAvailable() {
            try {
                const response = await fetch('/extract/check');

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('名额校验失败，HTTP状态:', response.status);
                    console.error('错误详情:', errorData);
                    throw new Error(errorData.detail || '名额校验失败');
                }

                const result = await response.json();

                if (!result.available) {
                    return { available: false, message: result.message };
                }

                return { available: true, message: null };

            } catch (error) {
                console.error('名额校验异常:', error);
                return {
                    available: false,
                    message: '名额校验失败：' + error.message
                };
            }
        }

        // 获取抽取数据（用于滚动显示）
        async function fetchExtractionData() {
            try {
                const response = await fetch('/extract/data');

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('获取数据失败:', errorData);
                    throw new Error(errorData.detail || '获取数据失败');
                }

                const data = await response.json();

                return {
                    success: true,
                    data: data
                };

            } catch (error) {
                console.error('获取数据异常:', error);
                return {
                    success: false,
                    message: '获取数据失败：' + error.message
                };
            }
        }

        // 开始抽取功能
        async function startExtraction(event) {
            // event.stopPropagation();

            if (isExtracting) return; // 防止重复点击

            // 检查是否在答题阶段，如果是则不允许开始抽题
            if (isCountdownRunning && !isPreparationPhase) {
                return;
            }

            // 检查按钮是否被禁用
            const mainActionButton = document.getElementById('mainActionButton');
            if (mainActionButton && mainActionButton.disabled) {
                return;
            }

            // 先校验名额
            const quotaCheck = await checkQuotaAvailable();
            if (!quotaCheck.available) {
                let customContent = `<div style="width: 100%;height: 100%;position: relative;display: flex;align-items: center;justify-content: center;">
                                            <div style="transform: scaleX(0.555);font-size: 50px;font-weight: 500;">
                                                <span>${quotaCheck.message}</span>
                                            </div>
                                            <div style="position: absolute;top: 84%;left: 45%;width: 21%;height: 13%;">
                                                <button type="button" class="layui-btn my-btn3" onclick="closeDialog()">关闭</button>
                                            </div>
                                        </div>`
                openCustomLayer(customContent)
                if (!quotaCheck.available) {
                    return;
                }
                return;
            }


            // 获取抽取数据
            const dataResult = await fetchExtractionData();
            if (!dataResult.success) {
                alert(dataResult.message);
                return;
            }

            // 名额充足且数据获取成功，开始抽取流程
            isExtracting = true;
            extractionStartTime = Date.now();

            // 切换按钮状态为抽取中
            const mainButton = document.getElementById('mainActionButton');
            if (mainButton) {
                mainButton.classList.add('extracting');
                mainButton.onclick = null;
            }

            // 禁用计时器相关按钮
            disableTimerButtons();

            // 使用获取的数据
            allPersons = dataResult.data.persons;
            allQuestions = dataResult.data.questions;

            // 第一次抽题后，始终保持first-show隐藏，result-show显示
            if (currentTimes == 1) {
                // 去掉 请开始抽题"开始抽题"按钮抽题 模块的覆盖
                let models1 = document.getElementsByClassName('first-show')
                models1[0].style.display = 'none'

                let models2 = document.getElementsByClassName('result-show')
                models2[0].style.display = 'block'
            } else {
                // 后续抽题时也保持同样的状态
                let models1 = document.getElementsByClassName('first-show')
                models1[0].style.display = 'none'

                let models2 = document.getElementsByClassName('result-show')
                models2[0].style.display = 'block'
            }

            // 开始播放抽题音效
            startExtractionSoundLoop();

            // 开始滚动显示
            startRollingDisplay();

            // 6秒后停止滚动并获取最终结果
            extractionTimeout = setTimeout(async () => {
                stopRollingAndShowResult();
            }, 6000);
        }



        // 开始滚动显示
        function startRollingDisplay() {
            // 添加滚动效果样式
            const resultItems = document.querySelectorAll('.result-show');
            resultItems.forEach(item => item.classList.add('rolling'));

            // 开始人员信息和题目滚动
            extractionInterval = setInterval(() => {
                const randomPerson = allPersons[Math.floor(Math.random() * allPersons.length)];
                const randomQuestion = allQuestions[Math.floor(Math.random() * allQuestions.length)];

                // 更新人员信息显示
                document.getElementById('nextName').textContent = randomPerson.name;
                document.getElementById('nextDept').textContent = randomPerson.department;
                document.getElementById('nextPosition').textContent = randomPerson.position;

                // 更新题目信息显示
                const nextQuestionElement = document.getElementById('nextQuestion');
                nextQuestionElement.textContent = randomQuestion.title;
                // 在滚动过程中不应用样式，避免影响滚动效果

            }, 80); // 每80ms更新一次，营造快速滚动效果
        }

        // 停止滚动并显示最终结果
        async function stopRollingAndShowResult() {
            // 立即停止抽题音效循环
            stopExtractionSoundLoop();

            // 停止滚动
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }

            // 移除滚动效果样式
            const resultItems = document.querySelectorAll('.result-show');
            resultItems.forEach(item => item.classList.remove('rolling'));

            try {
                // 调用后端API获取最终抽取结果
                const response = await fetch('/extract', { method: 'POST' });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '抽取失败');
                }

                const result = await response.json();

                // 显示最终结果
                document.getElementById('nextName').textContent = result.person.name;
                document.getElementById('nextDept').textContent = result.person.department;
                document.getElementById('nextPosition').textContent = result.person.position;

                // 显示下一轮结果区域
                document.querySelector('.first-show').style.display = 'none';
                document.querySelector('.result-show').style.display = 'block';

                // 处理下一轮题目显示样式
                const nextQuestionElement = document.getElementById('nextQuestion');
                // 使用setTimeout确保DOM更新完成
                setTimeout(() => {
                    handleQuestionDisplay(nextQuestionElement, result.question.title);
                }, 50);

                // 添加结果确定的视觉效果 - 只应用于文字值部分
                // setTimeout(() => {
                //     const resultTexts = document.querySelectorAll('.result-text');
                //     resultTexts.forEach(text => {
                //         text.style.color = '#dc3545';
                //         text.style.transform = 'scale(1.05)';
                //         text.style.textShadow = '3px 3px 8px rgba(220,53,69,0.5)';
                //     });
                // }, 100);

                // 更新按钮状态为"重新抽题"
                updateButtonToRestart();

                // 重新启用计时器相关按钮
                enableTimerButtons();

            } catch (error) {
                console.error('获取最终结果失败：', error);
                alert('抽取失败：' + error.message);
                resetExtractionState();
                // 发生错误时，重置按钮为开始抽题状态
                setupStartExtractionButton();
                // 重新启用计时器相关按钮
                enableTimerButtons();
            }
        }



        // 更新按钮状态为重新抽取
        function updateButtonToRestart() {
            // 设置按钮为重新抽题状态
            setupRestartExtractionButton();

            // 重置抽取状态
            isExtracting = false;
        }

        // 重置抽取状态
        function resetExtractionState() {
            isExtracting = false;

            // 停止滚动
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }

            // 清理6秒定时器
            if (extractionTimeout) {
                clearTimeout(extractionTimeout);
                extractionTimeout = null;
            }

            // 停止抽题音效循环
            stopExtractionSoundLoop();

            // 移除滚动效果样式
            const resultItems = document.querySelectorAll('.result-show');
            resultItems.forEach(item => {
                item.classList.remove('rolling');
            });

            // 重置文字值的样式
            const resultTexts = document.querySelectorAll('.result-text');
            resultTexts.forEach(text => {
                text.style.color = '#2c3e50';
                text.style.transform = 'scale(1)';
                text.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3)';
            });

            // 如果在第2页，设置按钮为开始抽题状态
            if (currentPage === 3) {
                setupStartExtractionButton();
            }

            // 重新启用计时器相关按钮
            enableTimerButtons();
        }

        // 暂停/继续计时功能
        function toggleTimer() {
            // 检查是否正在抽取中，如果是则不允许操作计时器
            if (isExtracting) {
                return;
            }

            if (!isCountdownRunning && !isCountdownPaused) {
                // 首次启动倒计时
                startCountdown();
            } else if (isCountdownRunning && !isCountdownPaused) {
                // 暂停倒计时
                pauseCountdown();
            } else if (isCountdownPaused) {
                // 继续倒计时
                resumeCountdown();
            }
        }

        // 启动倒计时
        function startCountdown() {
            // 根据当前阶段设置倒计时时间
            if (isPreparationPhase) {
                // 准备阶段，设置5分钟准备时间
                totalSeconds = preparationTime;
            } else {
                // 答题阶段，设置10分钟答题时间
                totalSeconds = answerTime;
            }

            isCountdownRunning = true;
            isCountdownPaused = false;

            // 清除之前的状态
            const countdownDisplay = document.getElementById('countdownDisplay');
            if (countdownDisplay) {
                countdownDisplay.classList.remove('countdown-warning', 'countdown-finished');
            }

            // 立即更新显示
            updateCountdownDisplay();

            // 更新按钮状态
            updateTimerButtonState();

            // 开始倒计时
            runCountdown();
        }

        // 暂停倒计时
        function pauseCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }

            isCountdownPaused = true;
            updateTimerButtonState();
        }

        // 继续倒计时
        function resumeCountdown() {
            isCountdownPaused = false;
            updateTimerButtonState();

            // 继续倒计时
            runCountdown();
        }

        // 运行倒计时逻辑
        function runCountdown() {
            countdownInterval = setInterval(function () {
                totalSeconds--;
                updateCountdownDisplay();

                // 1分钟提醒：播放强音提示
                if (totalSeconds === 60) {
                    playOneMinuteWarning();
                }

                // 最后5秒警告：持续提示
                if (totalSeconds <= 5 && totalSeconds > 0) {
                    document.getElementById('countdownDisplay').classList.add('countdown-warning');
                    // 播放提示音（每秒一次）
                    playBeep();
                }

                // 时间到
                if (totalSeconds <= 0) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                    isCountdownRunning = false;
                    isCountdownPaused = false;

                    // 更新显示为00:00
                    updateCountdownDisplay();

                    // 清除所有动画状态，时间为0时不显示动画效果
                    document.getElementById('countdownDisplay').classList.remove('countdown-warning', 'countdown-finished');

                    // 更新按钮状态
                    updateTimerButtonState();

                    // 检查是否是准备阶段结束
                    if (isPreparationPhase) {
                        // 准备阶段结束，自动进入答题阶段
                        startAnswerPeriod();
                    } else {
                        // 答题阶段结束，播放结束音并显示答题时间到弹框
                        playEndSound();
                        // 结束答题阶段，恢复按钮状态，并显示弹框（倒计时自然结束）
                        endAnswerPeriod(true);
                    }
                }
            }, 1000);
        }

        // 更新计时按钮状态
        function updateTimerButtonState() {
            const pauseBtn = document.getElementById('pauseTimerBtn');

            // 检查元素是否存在，避免JavaScript错误
            if (!pauseBtn) {
                return;
            }

            if (isCountdownPaused) {
                // 暂停状态：显示继续图标
                pauseBtn.classList.add('paused');
            } else {
                // 运行状态：显示暂停图标
                pauseBtn.classList.remove('paused');
            }
        }

        // 启动计时器功能（只用于第一个人的准备阶段）
        function startTimer() {
            // 检查是否正在抽取中，如果是则不允许操作计时器
            if (isExtracting) {
                return;
            }

            // 如果倒计时已经在运行，不执行任何操作
            if (isCountdownRunning) {
                return;
            }

            // 如果倒计时已暂停，则继续倒计时
            if (isCountdownPaused) {
                resumeCountdown();
                return;
            }

            // 只有处于准备阶段时才启动准备倒计时（仅第一个人）
            if (isPreparationPhase) {
                // 启动准备阶段倒计时
                isCountdownRunning = true;
                isCountdownPaused = false;

                // 将"开始"按钮改为"答题"按钮
                const startButton = document.querySelector('.timer-item .button-items .start-button-container1 .my-btn1');
                if (startButton) {
                    startButton.textContent = '答题';
                    startButton.onclick = function () {
                        startAnswerPeriod();
                    };
                }

                // 启动准备阶段倒计时
                runCountdown();
                return;
            }

            // 对于其他情况，直接进入答题阶段
            startAnswerPeriod();
        }

        // 直接开始答题阶段
        function startAnswerPeriod() {
            // 检查是否正在抽取中，如果是则不允许操作计时器
            if (isExtracting) {
                return;
            }

            // 如果倒计时已暂停，则继续倒计时
            if (isCountdownPaused) {
                resumeCountdown();
                return;
            }

            // 检查是否已经在答题阶段且倒计时正在运行，如果是则不允许重新开始
            if (isCountdownRunning && !isPreparationPhase) {
                return;
            }

            // 停止当前倒计时（如果正在运行）
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }

            // 结束准备阶段，进入答题阶段
            isPreparationPhase = false;
            // 更新计时器标题为答题时间
            document.querySelector('.timer-title').textContent = '距离答题结束还剩';
            // 设置答题时间
            totalSeconds = answerTime;
            // 更新显示
            updateCountdownDisplay();
            // 重置倒计时状态
            isCountdownRunning = true;
            isCountdownPaused = false;

            // 将切换按钮改为"结束"按钮
            const changeButton = document.querySelector('.timer-item .button-items .start-button-container1:nth-child(3) .my-btn1');
            if (changeButton) {
                changeButton.textContent = '结束';
                changeButton.onclick = function () {
                    endAnswerPeriod();
                };
            } else {
                console.error('未找到切换按钮');
            }

            // 禁用下一轮开始抽题按钮
            const mainActionButton = document.getElementById('mainActionButton');
            if (mainActionButton) {
                mainActionButton.disabled = true;
                mainActionButton.classList.add('disabled');
                mainActionButton.style.opacity = '0.5';
                mainActionButton.style.cursor = 'not-allowed';
                mainActionButton.style.pointerEvents = 'none';
            }

            // 禁用答题按钮，防止重复点击重启倒计时
            const answerButton = document.querySelector('.timer-item .button-items .start-button-container1 .my-btn1');
            if (answerButton) {
                answerButton.disabled = true;
                answerButton.classList.add('disabled');
                answerButton.style.opacity = '0.5';
                answerButton.style.cursor = 'not-allowed';
                answerButton.style.pointerEvents = 'none';
            }

            // 启动答题倒计时
            runCountdown();
        }

        // 结束答题阶段
        function endAnswerPeriod(showModal = false) {
            // 停止当前倒计时
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }

            // 重置倒计时状态
            isCountdownRunning = false;
            isCountdownPaused = false;

            // 将计时器直接置为0
            totalSeconds = 0;
            updateCountdownDisplay();

            // 将"结束"按钮改回"切换"按钮
            const changeButton = document.querySelector('.timer-item .button-items .start-button-container1:nth-child(3) .my-btn1');
            if (changeButton) {
                changeButton.textContent = '切换';
                changeButton.onclick = function () {
                    changeAnswer();
                };
            } else {
                console.error('未找到结束按钮');
            }

            // 重新启用下一轮开始抽题按钮
            const mainActionButton = document.getElementById('mainActionButton');
            if (mainActionButton) {
                mainActionButton.disabled = false;
                mainActionButton.classList.remove('disabled');
                mainActionButton.style.opacity = '1';
                mainActionButton.style.cursor = 'pointer';
                mainActionButton.style.pointerEvents = 'auto';
            }

            // 重新启用答题按钮
            const answerButton = document.querySelector('.timer-item .button-items .start-button-container1 .my-btn1');
            if (answerButton) {
                answerButton.disabled = false;
                answerButton.classList.remove('disabled');
                answerButton.style.opacity = '1';
                answerButton.style.cursor = 'pointer';
                answerButton.style.pointerEvents = 'auto';
            }

            // 只有在倒计时自然结束时才显示弹框
            if (showModal) {
                openCustomLayer(timeOverText);
            }
        }

        // 重置时间功能（只重置倒计时）
        function resetTimer() {
            // 检查是否正在抽取中，如果是则不允许重置计时器
            if (isExtracting) {
                return;
            }

            // 停止当前倒计时
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }

            // 重置倒计时状态
            isCountdownRunning = false;
            isCountdownPaused = false;
            totalSeconds = 480; // 重置为10分钟

            // 清除倒计时样式
            const countdownDisplay = document.getElementById('countdownDisplay');
            if (countdownDisplay) {
                countdownDisplay.classList.remove('countdown-warning', 'countdown-finished');
            }

            // 更新显示
            updateCountdownDisplay();

            // 更新按钮状态
            updateTimerButtonState();
        }

        // 更新倒计时显示
        function updateCountdownDisplay() {
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;

            const minutesElement = document.getElementById('minutes');
            const secondsElement = document.getElementById('seconds');

            if (minutesElement && secondsElement) {
                minutesElement.textContent = minutes.toString().padStart(2, '0');
                secondsElement.textContent = seconds.toString().padStart(2, '0');
            } else {
                console.error('找不到倒计时显示元素');
            }
        }

        // 播放提示音
        function playBeep() {
            const audioContext = getAudioContext();
            if (!audioContext) return;

            try {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                console.error('播放提示音失败:', e);
            }
        }

        // 播放结束音
        function playEndSound() {
            const audioContext = getAudioContext();
            if (!audioContext) return;

            try {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.5, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 1);
            } catch (e) {
                console.error('播放结束音失败:', e);
            }
        }

        // 播放1分钟提醒强音
        function playOneMinuteWarning() {
            const audioContext = getAudioContext();
            if (!audioContext) return;

            try {
                // 创建三个音调的和弦，产生更强烈的提示效果
                const frequencies = [600, 800, 1000]; // 三个频率组成和弦
                const oscillators = [];
                const gainNodes = [];

                frequencies.forEach((freq, index) => {
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
                    // 第一个音调音量最大，其他音调稍小
                    const volume = index === 0 ? 0.4 : 0.2;
                    gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8);

                    oscillators.push(oscillator);
                    gainNodes.push(gainNode);

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.8);
                });

            } catch (e) {
                console.error('播放1分钟提醒音失败:', e);
            }
        }

        // 抽题音效：固定使用铃铛和弦音效

        // 全局AudioContext实例，避免重复创建
        let globalAudioContext = null;

        // 获取或创建AudioContext
        function getAudioContext() {
            if (!globalAudioContext || globalAudioContext.state === 'closed') {
                try {
                    globalAudioContext = new (window.AudioContext || window.webkitAudioContext)();
                } catch (error) {
                    console.error('创建AudioContext失败:', error);
                    return null;
                }
            }

            // 如果AudioContext被暂停，尝试恢复
            if (globalAudioContext.state === 'suspended') {
                globalAudioContext.resume().catch(error => {
                    console.error('恢复AudioContext失败:', error);
                });
            }

            return globalAudioContext;
        }

        // 播放抽题提示音（铃铛和弦音效）
        function playExtractionSound() {
            try {
                playBellChimeSound();
            } catch (e) {
                console.error('播放抽题提示音失败:', e);
            }
        }



        // 音效4：铃铛和弦音效（选人选题专用优化版）
        function playBellChimeSound() {
            const audioContext = getAudioContext();
            if (!audioContext) return;

            try {
                const oscillator1 = audioContext.createOscillator();
                const oscillator2 = audioContext.createOscillator();
                const oscillator3 = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator1.connect(gainNode);
                oscillator2.connect(gainNode);
                oscillator3.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // 优化的三音和弦，更适合选人选题场景
                oscillator1.type = 'sine';
                oscillator2.type = 'sine';
                oscillator3.type = 'sine';

                // C大调三和弦：C5 + E5 + G5，营造正面、积极的氛围
                oscillator1.frequency.setValueAtTime(523, audioContext.currentTime); // C5 - 主音
                oscillator2.frequency.setValueAtTime(659, audioContext.currentTime); // E5 - 三音
                oscillator3.frequency.setValueAtTime(784, audioContext.currentTime); // G5 - 五音

                // 温和的音量包络，适合重复播放
                gainNode.gain.setValueAtTime(0.12, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.25);

                oscillator1.start(audioContext.currentTime);
                oscillator2.start(audioContext.currentTime);
                oscillator3.start(audioContext.currentTime);
                oscillator1.stop(audioContext.currentTime + 0.25);
                oscillator2.stop(audioContext.currentTime + 0.25);
                oscillator3.stop(audioContext.currentTime + 0.25);

            } catch (error) {
                console.error('播放铃铛和弦音效失败:', error);
            }
        }





        // 开始抽题音效循环播放
        function startExtractionSoundLoop() {
            // 确保先停止之前的循环
            if (extractionSoundInterval) {
                clearInterval(extractionSoundInterval);
                extractionSoundInterval = null;
            }

            // 检查是否还在抽取状态
            if (!isExtracting) {
                return;
            }

            // 立即播放一次
            playExtractionSound();

            // 每0.25秒播放一次（推荐频率）
            extractionSoundInterval = setInterval(() => {
                // 检查是否还在抽取状态
                if (!isExtracting) {
                    stopExtractionSoundLoop();
                    return;
                }
                playExtractionSound();
            }, 250);
        }

        // 停止抽题音效循环播放
        function stopExtractionSoundLoop() {
            if (extractionSoundInterval) {
                clearInterval(extractionSoundInterval);
                extractionSoundInterval = null;
            }
        }

        // 重新抽题功能
        async function restartExtraction(event) {
            event.stopPropagation();

            // 检查是否正在抽取中
            if (isExtracting) {
                return;
            }

            // 先校验名额
            const quotaCheck = await checkQuotaAvailable();
            if (!quotaCheck.available) {
                alert(quotaCheck.message);
                return;
            }

            // 获取抽取数据
            const dataResult = await fetchExtractionData();
            if (!dataResult.success) {
                alert(dataResult.message);
                return;
            }

            // 确保停止之前的所有定时器和音效
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }

            // 清理之前的6秒定时器
            if (extractionTimeout) {
                clearTimeout(extractionTimeout);
                extractionTimeout = null;
            }

            // 确保停止之前的音效循环
            stopExtractionSoundLoop();

            // 名额充足且数据获取成功，开始新的抽取流程
            isExtracting = true;
            extractionStartTime = Date.now();

            // 设置按钮为抽取中状态
            const mainButton = document.getElementById('mainActionButton');
            if (mainButton) {
                mainButton.classList.add('extracting');
                mainButton.onclick = null;
            }

            // 禁用计时器相关按钮
            disableTimerButtons();

            // 重置结果项样式 - 只重置文字值部分
            const resultTexts = document.querySelectorAll('.result-text');
            resultTexts.forEach(text => {
                text.style.color = '#2c3e50';
                text.style.transform = 'scale(1)';
                text.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3)';
            });

            // 重置显示内容
            document.getElementById('nextName').textContent = '正在抽取...';
            document.getElementById('nextDept').textContent = '正在抽取...';
            document.getElementById('nextPosition').textContent = '正在抽取...';
            document.getElementById('nextQuestion').textContent = '正在抽取...';

            // 使用获取的数据
            allPersons = dataResult.data.persons;
            allQuestions = dataResult.data.questions;

            // 开始播放抽题音效
            startExtractionSoundLoop();

            // 开始滚动显示
            startRollingDisplay();

            // 6秒后停止滚动并获取最终结果
            extractionTimeout = setTimeout(async () => {
                stopRollingAndShowResult();
            }, 6000);
        }

        // 结束会话功能（将计时器置为0，等待空格键回到页面1）
        function endSession() {
            // 检查是否正在抽取中，如果是则不允许结束会话
            if (isExtracting) {
                return;
            }

            // 停止所有定时器
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }
            if (extractionTimeout) {
                clearTimeout(extractionTimeout);
                extractionTimeout = null;
            }

            // 停止抽题音效循环
            stopExtractionSoundLoop();

            // 设置倒计时为0并显示
            totalSeconds = 0;
            isCountdownRunning = false;
            isCountdownPaused = false;

            // 更新显示为00:00
            updateCountdownDisplay();

            // 清除所有动画状态样式，不添加结束状态动画
            const countdownDisplay = document.getElementById('countdownDisplay');
            if (countdownDisplay) {
                countdownDisplay.classList.remove('countdown-warning', 'countdown-finished');
            }

            // 更新按钮状态
            updateTimerButtonState();

            // 启用空格键监听，等待用户按空格键回到页面1
            enableSpaceKeyListener();
        }

        // 显示答题时间到弹框
        function showTimeUpModal() {
            // 使用自定义弹框显示答题结束
            openCustomLayer(timeOverText);
        }

        // 关闭答题时间到弹框
        function closeTimeUpModal() {

            const modal = document.getElementById('timeUpModal');
            if (modal) {
                modal.classList.add('hide');
                modal.classList.remove('show');

                // 动画结束后隐藏弹框
                setTimeout(() => {
                    modal.style.display = 'none';
                    // 恢复页面滚动
                    document.body.style.overflow = 'hidden'; // 保持hidden因为是全屏应用
                }, 300);
            }
        }


    </script>
</body>

</html>